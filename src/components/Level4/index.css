@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Fixed viewport setup - allow scrolling if content overflows */
html, body, #root {
  height: 100vh;
  min-height: 0;
  overflow: auto;
  margin: 0;
  padding: 0;
  font-family: 'Orbitron', sans-serif;
}

/* Mobile orientation requirement */
@media (max-width: 768px) and (orientation: portrait) {
  .rotation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: white;
    text-align: center;
    padding: 2rem;
  }

  .rotate-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: rotateAnimation 2s ease-in-out infinite;
  }

  @keyframes rotateAnimation {
    0% { transform: rotate(0deg); }
    25% { transform: rotate(90deg); }
    50% { transform: rotate(90deg); }
    75% { transform: rotate(90deg); }
    100% { transform: rotate(90deg); }
  }

  .main-content {
    display: none;
  }
}

@media (max-width: 768px) and (orientation: landscape) {
  html, body, #root {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: 100vh;
    min-height: 0;
  }
  .deviation-card {
    max-height: 90vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .rotation-overlay {
    display: none;
  }
  
  .main-content {
    display: block;
  }
}

@media (min-width: 769px) {
  .rotation-overlay {
    display: none;
  }
  
  .main-content {
    display: block;
  }
}

/* Custom 2D Effects for Deviation Detective Game */
@layer utilities {
  .bg-grid-pattern {
    background-image: 
      linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
  
  .animate-slide-in-left {
    animation: slideInLeft 0.5s ease-out;
  }
  
  .animate-slide-in-right {
    animation: slideInRight 0.5s ease-out;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .animate-bounce-subtle {
    animation: bounceSubtle 2s infinite;
  }
  
  .transform-gpu {
    transform: translateZ(0);
  }
  
  .perspective-1000 {
    perspective: 1000px;
  }
  
  .preserve-3d {
    transform-style: preserve-3d;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounceSubtle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

/* Enhanced button effects */
.btn-detective {
  position: relative;
  overflow: hidden;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  transition: all 0.3s ease;
}

.btn-detective::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-detective:hover::before {
  left: 100%;
}

.btn-detective:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

/* Card hover effects */
.card-detective {
  transition: all 0.3s ease;
  position: relative;
}

.card-detective:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-detective::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.card-detective:hover::after {
  opacity: 1;
}

/* Animated progress indicators */
.progress-dot {
  position: relative;
}

.progress-dot.active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid currentColor;
  border-radius: 50%;
  animation: ripple 2s infinite;
}

@keyframes ripple {
  0% {
    width: 20px;
    height: 20px;
    opacity: 1;
  }
  100% {
    width: 40px;
    height: 40px;
    opacity: 0;
  }
}

/* Data stream animation */
.data-stream {
  position: relative;
  overflow: hidden;
}

.data-stream::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, #3b82f6, transparent);
  animation: dataFlow 2s linear infinite;
}

@keyframes dataFlow {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}
