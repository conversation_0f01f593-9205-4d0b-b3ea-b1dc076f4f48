/* Enhanced Game Roadmap Styles */

/* Desktop optimizations */
@media screen and (min-width: 1024px) {
  .game-roadmap-container {
    padding: 2rem;
  }
  
  .game-roadmap-svg {
    min-height: 700px;
  }
  
  /* Enhanced module stone hover effects for desktop */
  .module-stone-hover:hover {
    filter: drop-shadow(0 0 20px rgba(245, 158, 11, 0.6));
    transform: scale(1.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Better spacing for desktop UI elements */
  .desktop-ui-spacing {
    gap: 2rem;
  }
  
  /* Enhanced progress indicator for desktop */
  .progress-indicator-desktop {
    min-width: 300px;
    backdrop-filter: blur(10px);
  }
}

/* Tablet optimizations */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .game-roadmap-container {
    padding: 1.5rem;
  }
  
  .game-roadmap-svg {
    min-height: 600px;
  }
  
  /* Tablet-specific module stone sizing */
  .module-stone-tablet {
    transform: scale(0.9);
  }
}

/* Mobile landscape optimizations */
@media screen and (max-width: 767px) and (orientation: landscape) {
  .game-roadmap-container {
    padding: 0.25rem;
  }

  .game-roadmap-svg {
    min-height: 350px;
  }

  /* Compact UI for mobile landscape */
  .mobile-landscape-ui {
    scale: 0.7;
  }

  /* Smaller progress indicator for mobile landscape */
  .progress-indicator-desktop {
    min-width: 180px !important;
    padding: 0.75rem !important;
    top: 0.5rem !important;
    right: 0.5rem !important;
  }

  /* Compact back button for mobile landscape */
  .back-button-mobile {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
    top: 0.5rem !important;
    left: 0.5rem !important;
  }

  /* Reduce spacing in progress dots */
  .progress-indicator-desktop .flex {
    gap: 0.25rem !important;
  }

  /* Smaller text in mobile landscape */
  .progress-indicator-desktop .roadmap-text {
    font-size: 0.75rem !important;
    line-height: 1.2 !important;
  }

  /* Hide some text on very small screens */
  @media screen and (max-width: 640px) and (orientation: landscape) {
    .progress-indicator-desktop {
      min-width: 150px !important;
      padding: 0.5rem !important;
    }

    /* Make progress bar thinner */
    .progress-indicator-desktop .bg-gray-200 {
      height: 3px !important;
    }

    /* Smaller progress dots */
    .progress-indicator-desktop .w-3 {
      width: 8px !important;
      height: 8px !important;
    }
  }
}

/* Mobile portrait optimizations */
@media screen and (max-width: 767px) and (orientation: portrait) {
  .game-roadmap-container {
    padding: 1rem;
  }
  
  .game-roadmap-svg {
    min-height: 500px;
  }
  
  /* Stack UI elements vertically on mobile portrait */
  .mobile-portrait-ui {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
}

/* High DPI display optimizations */
@media screen and (min-resolution: 2dppx) {
  .game-roadmap-svg {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
  
  /* Sharper text rendering */
  .roadmap-text {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Animation enhancements */
@keyframes roadmap-entrance {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes module-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes path-glow {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(255, 228, 181, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(255, 228, 181, 0.6));
  }
}

/* Apply animations */
.roadmap-entrance {
  animation: roadmap-entrance 0.8s ease-out;
}

.module-pulse-animation {
  animation: module-pulse 2s ease-in-out infinite;
}

.path-glow-animation {
  animation: path-glow 3s ease-in-out infinite;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .module-stone-hover:hover,
  .roadmap-entrance,
  .module-pulse-animation,
  .path-glow-animation {
    animation: none;
    transition: none;
  }
}

/* Focus states for keyboard navigation */
.module-stone-focus:focus {
  outline: 3px solid #F59E0B;
  outline-offset: 4px;
  border-radius: 50%;
}

/* Enhanced contrast for better visibility */
@media (prefers-contrast: high) {
  .roadmap-text {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  }
  
  .module-stone-border {
    stroke-width: 6px;
  }
}

/* Print styles (if needed) */
@media print {
  .game-roadmap-container {
    background: white !important;
  }
  
  .roadmap-interactive-elements {
    display: none;
  }
}

/* Custom scrollbar for webkit browsers */
.game-roadmap-container::-webkit-scrollbar {
  width: 8px;
}

.game-roadmap-container::-webkit-scrollbar-track {
  background: rgba(245, 158, 11, 0.1);
  border-radius: 4px;
}

.game-roadmap-container::-webkit-scrollbar-thumb {
  background: rgba(245, 158, 11, 0.5);
  border-radius: 4px;
}

.game-roadmap-container::-webkit-scrollbar-thumb:hover {
  background: rgba(245, 158, 11, 0.7);
}

/* Utility classes for game roadmap */
.roadmap-shadow-soft {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.roadmap-shadow-medium {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.roadmap-shadow-strong {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.roadmap-blur-backdrop {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Game-style border effects */
.game-border-glow {
  border: 2px solid transparent;
  background: linear-gradient(45deg, #F59E0B, #D97706) border-box;
  border-radius: 12px;
}

.game-border-glow::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(45deg, #F59E0B, #D97706);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}
