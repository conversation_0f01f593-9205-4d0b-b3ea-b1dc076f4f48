import type { Module } from '../types';

export const GMP_MODULES: Module[] = [
  {
    id: 1,
    title: "GMP & Regulations",
    description: "Foundation of pharmaceutical regulations and GMP principles",
    icon: "📋",
    color: "#4F46E5",
    position: { x: 10, y: 80 },
    completed: false,
    unlocked: true,
    levels: [
      {
        id: 1,
        name: "Introduction to GMP",
        description: "Understand the basics of Good Manufacturing Practices.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "Regulatory Bodies",
        description: "Learn about the key regulatory agencies in pharmaceuticals.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "GMP Guidelines",
        description: "Explore detailed guidelines and requirements of GMP.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "GMP Analysis",
        description: "Analyze complex GMP scenarios and case studies.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
  {
    id: 2,
    title: "Pharma Functions",
    description: "Core pharmaceutical manufacturing and quality functions",
    icon: "🏭",
    color: "#059669",
    position: { x: 25, y: 60 },
    completed: false,
    unlocked: false,
    levels: [
      {
        id: 1,
        name: "Manufacturing Processes",
        description: "Overview of pharmaceutical manufacturing techniques.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "Quality Control",
        description: "Methods for ensuring product quality and consistency.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "Packaging and Labeling",
        description: "Requirements for packaging and labeling of drug products.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "Pharma Functions Analysis",
        description: "Analyze complex Pharma Functions scenarios and case studies.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
  {
    id: 3,
    title: "GDocP & Data Integrity",
    description: "Good Documentation Practices and maintaining data integrity",
    icon: "📊",
    color: "#DC2626",
    position: { x: 45, y: 70 },
    completed: false,
    unlocked: false,
    levels: [
      {
        id: 1,
        name: "Documentation Basics",
        description: "Introduction to good documentation practices.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "Data Integrity Principles",
        description: "Understanding ALCOA principles in data management.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "Electronic Records",
        description: "Managing electronic records and signatures.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "GDocP Analysis",
        description: "Analyze complex GDocP scenarios and data integrity issues.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
  {
    id: 4,
    title: "Self-Inspection & Audit",
    description: "Internal auditing and self-inspection processes",
    icon: "🔍",
    color: "#7C2D12",
    position: { x: 65, y: 50 },
    completed: false,
    unlocked: false,
    levels: [
      {
        id: 1,
        name: "Audit Preparation",
        description: "Preparing for internal and external audits.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "Conducting Audits",
        description: "Techniques for conducting effective audits.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "Audit Reporting",
        description: "Writing and managing audit reports.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "Audit Analysis",
        description: "Analyze audit findings and develop improvement plans.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
  {
    id: 5,
    title: "Qualification & Validation",
    description: "Equipment qualification and process validation activities",
    icon: "✅",
    color: "#B45309",
    position: { x: 80, y: 70 },
    completed: false,
    unlocked: false,
    levels: [
      {
        id: 1,
        name: "Validation Basics",
        description: "Introduction to validation processes.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "Equipment Qualification",
        description: "Qualifying pharmaceutical equipment.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "Process Validation",
        description: "Validating manufacturing processes.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "Validation Analysis",
        description: "Analyze validation data and ensure compliance.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
  {
    id: 6,
    title: "Change Control",
    description: "Managing changes to systems, processes, and procedures",
    icon: "🔄",
    color: "#7C3AED",
    position: { x: 85, y: 40 },
    completed: false,
    unlocked: false,
    levels: [
      {
        id: 1,
        name: "Change Control Intro",
        description: "Introduction to change control management.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "Implementing Changes",
        description: "Implementing approved changes in a GMP environment.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "Change Documentation",
        description: "Documenting and tracking changes.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "Change Control Analysis",
        description: "Analyze change control processes and outcomes.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
  {
    id: 7,
    title: "Deviation Management",
    description: "Handling deviations, investigations, and corrective actions",
    icon: "⚠️",
    color: "#0891B2",
    position: { x: 75, y: 20 },
    completed: false,
    unlocked: false,
    levels: [
      {
        id: 1,
        name: "Deviation Basics",
        description: "Understanding deviations in manufacturing.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "Deviation Investigation",
        description: "Investigating and documenting deviations.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "CAPA Implementation",
        description: "Implementing corrective and preventive actions.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "Deviation Analysis",
        description: "Analyze deviation trends and prevention strategies.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
  {
    id: 8,
    title: "OOS (Out of Specification)",
    description: "Managing out-of-specification results and investigations",
    icon: "📉",
    color: "#65A30D",
    position: { x: 55, y: 30 },
    completed: false,
    unlocked: false,
    levels: [
      {
        id: 1,
        name: "OOS Introduction",
        description: "Introduction to OOS results.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "OOS Investigation",
        description: "Investigating OOS results.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "OOS Reporting",
        description: "Reporting and documenting OOS results.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "OOS Analysis",
        description: "Analyze OOS data and improve processes.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
  {
    id: 9,
    title: "RCA & CAPA",
    description: "Root Cause Analysis and Corrective/Preventive Action systems",
    icon: "🔬",
    color: "#BE185D",
    position: { x: 35, y: 40 },
    completed: false,
    unlocked: false,
    levels: [
      {
        id: 1,
        name: "RCA Basics",
        description: "Understanding root cause analysis.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "CAPA Planning",
        description: "Planning corrective and preventive actions.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "CAPA Implementation",
        description: "Implementing CAPA effectively.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "RCA & CAPA Analysis",
        description: "Analyze RCA and CAPA effectiveness and trends.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
  {
    id: 10,
    title: "QRM (Quality Risk Management)",
    description: "Quality risk management principles and applications",
    icon: "🛡️",
    color: "#EA580C",
    position: { x: 20, y: 20 },
    completed: false,
    unlocked: false,
    levels: [
      {
        id: 1,
        name: "QRM Introduction",
        description: "Introduction to quality risk management.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "Risk Assessment",
        description: "Assessing risks in pharmaceutical processes.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "Risk Control",
        description: "Controlling and mitigating risks.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "QRM Analysis",
        description: "Analyze quality risk management systems and outcomes.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
  {
    id: 11,
    title: "Complaint & Recall",
    description: "Customer complaint handling and product recall management",
    icon: "📞",
    color: "#0F766E",
    position: { x: 40, y: 10 },
    completed: false,
    unlocked: false,
    levels: [
      {
        id: 1,
        name: "Complaint Handling",
        description: "Handling customer complaints effectively.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "Recall Initiation",
        description: "Initiating a product recall.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "Recall Management",
        description: "Managing and executing product recalls.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "Complaint & Recall Analysis",
        description: "Analyze complaint and recall data for improvements.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
  {
    id: 12,
    title: "Retention Samples",
    description: "Sample retention, storage, and testing management",
    icon: "🧪",
    color: "#9333EA",
    position: { x: 60, y: 5 },
    completed: false,
    unlocked: false,
    levels: [
      {
        id: 1,
        name: "Sample Retention",
        description: "Understanding sample retention requirements.",
        difficulty: "Beginner",
        stars: 2,
        taxonomy: "Recall",
        time: 15,
      },
      {
        id: 2,
        name: "Sample Storage",
        description: "Storing retention samples correctly.",
        difficulty: "Intermediate",
        stars: 3,
        taxonomy: "Classify",
        time: 20,
      },
      {
        id: 3,
        name: "Sample Testing",
        description: "Testing retention samples.",
        difficulty: "Advanced",
        stars: 3,
        taxonomy: "Apply",
        time: 25,
      },
      {
        id: 4,
        name: "Retention Samples Analysis",
        description: "Analyze retention sample data and testing results.",
        difficulty: "Expert",
        stars: 4,
        taxonomy: "Analyze",
        time: 30,
      },
    ],
  },
];
